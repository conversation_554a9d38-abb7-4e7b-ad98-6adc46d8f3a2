
You are a senior Python developer and experimental psychologist. Your task is to develop a simple and powerful experiment building platform that can deploy ready-made experiments in HTML format. An inspiration for this program is [[Psytoolkit]], another free experiment generator built in the C language that outputs fully self-contained HTML files as experiments. 

Given the complexity of this program, we will iterate development in simple steps. I will describe what the goal is, and which steps have been achieved. 


===========

<step1> # COMPLETED

Goal: Create a basic working interface

Overview: A spreadsheet-like input structure with three columns, labelled 'Stimulus', 'Response', 'Latency'. The row numbers can be fixed at five for the display, but it should be possible to add new rows using a '+' icon underneath the last row.  Beneath that should be three buttons called 'Start', 'Save', and 'Load'. Other than the 'Start' button, which will compile and run the task, the remaining buttons can be placeholders for now. 

Details: 
Under 'Stimulus', the user can enter any combination of characters, including spaces. This shows the text to display. Under 'Response', the user can enter any number of acceptable responses, separated by commas. Under 'Latency', the number of milliseconds that said stimulus will appear will be provided. Note that both 'Response' and 'Latency' options have a 'NA' input option. That is, when 'Response' is NA, the stimulus will progress based on the latency condition only. When the latency is NA, the stimulus will progress when there is a response recorded only. If the user enters NA for both Response and Latency, pressing the Start button will produce a warning message stating that at least one of those properties have to be non-NA. If all inputs are correct (examples provided below), than pressing Start will display a 'Compiling' message, then run the task. All stimuli can be presented in a white Arial times 14 font against a dark grey background on screen center. 

Examples:

(A simple dot probe task)

Stimulus | Response | Latency
-------------------------------------------
+ | NA | 500 
. |space, n | 500
  | NA | 2000



(An appetitive conditioning task)

Stimulus | Response | Latency
-------------------------------------------
+ | NA | 100
eating | NA | 500
 | NA | 200
happy | NA | 500
do you feel hungry? | y, n | NA

</step1>

===========

<step2>  # COMPLETED

Goal: Add presentation sequencing

Overview: Now that <step1> has been achieved, let's control for presentation sequence, please make the following additions. First, add a new column called 'Block', to the left of Stimulus. Second, add a checkbox (default: ticked) beside the label 'Randomize Blocks?'.

Details: In the new column called 'Block', the user can enter integer values. This will determine which trial segments will appear in which sequence. Within a block, the trial sequence will follow as before, from top to bottom. Additionally, if the user checks the 'Randomize Blocks?' checkmark, then the sequence of block presentation will be randomized each iteration. 

Examples:

(Combining a dot probe and appetitive conditioning into a single task followed by a task end message - non-random)

Block | Stimulus | Response | Latency
-------------------------------------------
1 | + | NA | 500 
1 | . |space, n | 500
1 |   | NA | 2000
1 | + | NA | 100
2 | eating | NA | 500
2 | | NA | 200
2 |happy | NA | 500
2 |do you feel hungry? | y, n | NA
3 |thank you for taking part! | NA | 3000


</step2>

===========

<step3>   # COMPLETED

Goal: Save and Load study files

Overview: Now that <step1> and <step2> have been achieved, implement Save and Load functions. Also delete the Deploy button as this is redundant.

Details: Study configurations can be saved as simple TXT or, if necessary, JSON files. Ideally these could be saved/uploaded as comma separated plain text. For example, the previous task could be saved/loaded as:

Example: 
(save/load file in text format)

Block, Stimulus, Response, Latency
1, +, NA, 500 
1, .,space, n, 500
1,  , NA, 2000
1, +, NA, 100
2, eating, NA, 500
2,, NA, 200
2,happy, NA, 500
2,do you feel hungry?, y, n, NA
3,thank you for taking part!, NA, 3000

</step3>

===========

<step4>   # COMPLETED

Goal: Allow Special Keys (spacebar, control, alt, lshift, rshift)

Overview: The response only accepts individual letter keys, and needs to recognize special keys. This could be achieved by pre-programing target words, such as 'space', 'ctrl', 'alt', 'lshift', and/or 'rshift'.

Example:

(updated dot probe task)

Block | Stimulus | Response | Latency
-------------------------------------------
1 | + | NA | 500 
1 | . |space, n | 500
1 |  | NA | 2000

</step4>

===========

<step5>    # COMPLETED

Goal: Allow coding multiple responses inside Saved and Loaded Text files

Overview:  Allow several response options to be coded at once. 

Example:

(text version of dot probe task from step 4)

Block, Stimulus, Response, Latency
1, +, NA, 500 
1, ., (space, n), 500
1,  NA, 2000

</step5>

===========

<step6>    # COMPLETED

Goal: Ensure Stimulus and Response are accurately presented and recorded

Overview: Replace the setTimeout function in JavaScript with requestAnimationFrame for more accurate stimulus timing, which accounts for individual browser refresh rates. Also, accurately measure and record the time between stimulus onset and participant response.

Details: The current implementation uses setTimeout for timing, which can be inconsistent across different browsers and hardware. The requestAnimationFrame API provides more precise timing control that synchronizes with the browser's rendering cycle. Additionally, we need to implement accurate response time measurement by recording the timestamp when a stimulus appears and when a response is made.

</step6>

===========

<step7>    # COMPLETED
Goal: Data recording and measurement

Overview: Ensure completing the HTML experiment produces a comprehensive data file that captures all relevant experimental information. The experiment should automatically generate and download a JSON file containing all trial data when the experiment concludes.

Details: Implement a robust data recording system that builds upon the trial data collection introduced in Step 6. The system should:
1. Create a structured JSON file with experiment metadata (date, time, browser information)
2. Record detailed information for each trial including:
   - Block number
   - Trial number/index
   - Stimulus content
   - Expected responses
   - Participant's actual response
   - Response time in milliseconds
   - Timestamp
3. Implement an automatic download mechanism that triggers when the experiment completes
4. Include a unique identifier for each experiment session
5. Format the data in a way that's easily importable into analysis software like R, Python, or SPSS

Example JSON structure:
```json
{
  "experiment_id": "exp_12345",
  "timestamp": "2023-06-15T14:30:22.534Z",
  "browser_info": "Chrome 114.0.5735.133",
  "randomized_blocks": true,
  "trials": [
    {
      "block_num": 1,
      "trial_index": 0,
      "stimulus": "+",
      "expected_responses": ["NA"],
      "actual_response": null,
      "response_time": null,
      "latency": 500,
      "timestamp": "2023-06-15T14:30:25.123Z"
    },
    // Additional trials...
  ]
}
```

===========

<step8> # COMPLETED

Goal: Configurable Stimulus and Background Colors

Overview: Allow researchers to customize the visual appearance of stimuli and background for each trial, enhancing experimental flexibility and visual control.

Details: 
1. Add two new columns to the experiment generator interface:
   - "Stimulus Color" - for specifying the color of the stimulus text
   - "Background Color" - for specifying the background color of the experiment screen
2. Both columns should accept:
   - Standard color names (e.g., 'blue', 'red', 'green')
   - Hexadecimal color codes (e.g., '#0000FF', '#FF0000')
   - RGB values (e.g., 'rgb(0,0,255)')
3. If left blank, default values will be used (white text on dark grey background, as currently implemented)
4. The color values will be passed to the generate_html function and injected into the CSS style block
5. Colors can be specified globally for the entire experiment or individually for each trial

Example:

Block | Stimulus | Response | Latency | Stimulus Color | Background Color
-------------------------------------------
1 | + | NA | 500 | white | #333333
1 | . | space, n | 500 | #FF0000 | black
1 |   | NA | 2000 | yellow | #222222

</step8>

===========

<step9> # COMPLETED
Goal: Include text entry response option

Overview: Expand the experiment capabilities to include free-text responses, allowing for more diverse experimental paradigms such as memory recall, linguistic studies, and open-ended questions.

Details:
1. Implement a special marker [text] in the Response column to indicate that a text input field should be displayed
2. When [text] is detected, the generated JavaScript will:
   - Create an HTML <input type="text"> field and a 'Submit' button
   - Focus the cursor on the text input field automatically
   - Adapt the event listener to wait for the Submit button click instead of a keypress
   - Capture the entered text and store it as part of the trial data
3. Optional parameters can be included after [text] in parentheses:
   - [text(placeholder="Your answer here")] - Sets placeholder text
   - [text(maxlength=50)] - Limits the maximum number of characters
   - [text(width=300)] - Sets the width of the text field in pixels
4. The text response will be recorded in the data file along with other trial information

Example:

Block | Stimulus | Response | Latency
-------------------------------------------
1 | What is your favorite color? | [text] | NA
2 | List three animals you saw today | [text(placeholder="Type your answer", maxlength=100)] | NA
</step9>

===========

<step10> # COMPLETED
Goal: Include option to repeat specific blocks

Overview: Enhance experimental design flexibility by allowing researchers to repeat blocks of trials multiple times, with optional randomization between repetitions.

Details:
1. Add a new interface element called "Repeat Sequence" with a numeric input field (default: 1)
2. This setting will determine how many times the entire block sequence will be repeated
3. The repetition behavior will interact with the "Randomize Blocks?" checkbox:
   - If checked: Block order will be re-randomized before each repetition
   - If unchecked: Blocks will repeat in the same order each time
4. Implement a block counter in the data recording to track which repetition a trial belongs to
5. Add an optional "Block Label" column that allows researchers to give meaningful names to blocks for easier identification in the data

Example with 3 blocks (B1, B2, B3):
- With Repeat Sequence = 1: Each block appears once (e.g., B1, B2, B3)
- With Repeat Sequence = 3 and Randomize Blocks checked: 
  - First iteration: B2, B1, B3
  - Second iteration: B1, B3, B2
  - Third iteration: B3, B2, B1
- With Repeat Sequence = 3 and Randomize Blocks unchecked:
  - All three iterations: B1, B2, B3, B1, B2, B3, B1, B2, B3
</step10>

===========


<step11>  # COMPLETED
Goal: Incorporate optional, response contingent feedback

Overview: Enable researchers to provide immediate feedback to participants based on their responses, enhancing the interactive nature of experiments and allowing for learning paradigms.

Details:
1. Add a new column called "Feedback" to the experiment generator interface
2. The Feedback column can contain:
   - Text to display as feedback after a response
   - Special markers for conditional feedback:
     - [correct] "Good job!" - Shows "Good job!" only if the response matches one of the expected responses
     - [incorrect] "Try again" - Shows "Try again" only if the response doesn't match expected responses
     - [all] "Response recorded" - Shows regardless of response correctness
3. If the Feedback cell is left blank, no feedback will be displayed
4. Feedback will appear for a configurable duration (default: 1000ms) before advancing to the next trial
5. Add an optional "Feedback Duration" column to control how long feedback is displayed
6. Record in the data file whether feedback was shown and what type

Example:

Block | Stimulus | Response | Latency | Feedback | Feedback Duration
-------------------------------------------
1 | 2+2=? | 4 | NA | [correct] "Correct!" | 1000
1 | 3+5=? | 8 | NA | [correct] "Right!" [incorrect] "The answer is 8" | 1500
1 | Press any key | NA | 500 | [all] "Key detected" | 800

</step11>

===========

<step12>
Goal: Configurable Stimulus Type (Images)  # COMPLETED

Overview: Expand the experiment capabilities to include image stimuli, allowing for visual cognition studies, recognition tasks, and multimedia experiments.

Details:
1. Enhance the Stimulus column to accept image file references using a special syntax: [image:filename.jpg]
2. Implement a file upload mechanism in the interface to allow researchers to add images to the experiment
3. Store uploaded images in a designated folder within the experiment directory
4. When generating the HTML, configure it to reference these image files directly rather than embedding them as base64
5. Support common image formats: JPG, PNG, GIF, SVG
6. Add options for image sizing and positioning:
   - [image:filename.jpg(width=300,height=200)] - Sets specific dimensions
   - [image:filename.jpg(center)] - Centers the image
   - [image:filename.jpg(top-left)] - Positions at the top-left
7. Allow combining text and images in the same stimulus:
   - "Look at this: [image:star.png]"
8. Implement proper preloading of images to ensure they display immediately when needed
9. Allow positioning of image across 9 hard-coded positions 

Example:

Block | Stimulus | Response | Latency
-------------------------------------------
1 | [image:fixation.png] | NA | 500
1 | [image:target.jpg(width=400)] | space, n | 500
2 | Rate this image: [image:scene.jpg, top-right] | 1, 2, 3, 4, 5 | NA

</step12>

===========

<step13> # COMPLETED

Goal: Parse blocks to be repeated from blocks to be presented in sequence. 

Overview: The user can determine which blocks to present sequentially, and which to repeat based on pre-determined criteria.

Details: The current iteration of the PEG has a 'Repeat Sequence' option that allows the user to repeat all the blocks as many times as desired. This can be an issue whenever there are instructions/end screens, or different task rules (e.g. training vs testing).  There are two potential solutions. One is to complicate the 'peg_main.py' script by permitting the repeat counter to be set for different blocks. A second solution is to generate an 'experiment orchestrator' script that organizes the HTML files produced by the 'peg_main.py' file. While this is more cumbersome initially, this can eventually handle experiments with multiple blocks, some of which can repeat, some of which can contain instructions, etc. 

</step13>

===========

]

<future_goals>

===========


<step14>

Goal: Implement Conditional Trial Sequencing

Overview: Allow for dynamic experiment flow based on participant responses, enabling adaptive testing, branching narratives, and conditional experimental designs.

Details:
1. Add a new column called "Next" to specify which trial should follow the current one based on the response
2. The Next column can contain:
   - A trial number to jump to
   - Special markers like [next] (default behavior), [end] (end experiment), or [block:X] (jump to block X)
   - Conditional logic using the format: [if:response=X,then:trial=Y,else:trial=Z]
3. Implement a trial indexing system that allows referencing specific trials
4. Add support for logical operators in conditions (AND, OR, NOT)
5. Record the actual path taken through the experiment in the data file

Example:

ID | Block | Stimulus | Response | Latency | Next
-------------------------------------------
1 | 1 | Do you want the hard version? | y, n | NA | [if:response=y,then:block:2,else:block:3]
2 | 2 | Hard version selected | NA | 1000 | [next]
3 | 3 | Easy version selected | NA | 1000 | [next]

</step14>

===========

<step15>
Goal: Support for Audio Stimuli

Overview: Extend the experiment platform to include audio stimuli, enabling auditory perception studies, language processing experiments, and multimodal research.

Details:
1. Enhance the Stimulus column to accept audio file references using a special syntax: [audio:filename.mp3]
2. Implement a file upload mechanism for audio files similar to the image upload system
3. Support common audio formats: MP3, WAV, OGG
4. Add controls for audio playback:
   - [audio:tone.mp3(autoplay)] - Automatically plays when the trial starts
   - [audio:word.mp3(loop)] - Loops the audio until a response is made
   - [audio:sound.mp3(controls)] - Shows audio controls to the participant
5. Allow combining audio with text or images:
   - "Listen carefully: [audio:instruction.mp3]"
   - "[image:face.jpg] [audio:voice.mp3]"
6. Implement proper preloading of audio files to minimize playback delays
7. Record audio playback events (start, end, pause) in the data file

Example:

Block | Stimulus | Response | Latency
-------------------------------------------
1 | [audio:beep.mp3(autoplay)] | NA | 500
1 | [audio:word.mp3(controls)] What word did you hear? | [text] | NA
2 | [image:scene.jpg] [audio:description.mp3] | space | NA
</step15>

===========

<step16>


<step/16>