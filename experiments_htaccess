# .htaccess for /experiments/ directory
# Allows access to PHP scripts but protects sensitive files

# Allow PHP scripts to run
<Files "*.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

# Allow HTML experiment files to be accessed
<Files "*.html">
    Order Allow,Deny
    Allow from all
</Files>

# Allow CSS and JS files for experiments
<Files "*.css">
    Order Allow,Deny
    Allow from all
</Files>

<Files "*.js">
    Order Allow,Deny
    Allow from all
</Files>

# Block access to configuration and sensitive files
<Files "*.ini">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.conf">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# Block access to backup files
<Files "*~">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.bak">
    Order Deny,Allow
    Deny from all
</Files>

# Block access to version control files
<Files ".git*">
    Order Deny,Allow
    Deny from all
</Files>

<Files ".svn*">
    Order Deny,Allow
    Deny from all
</Files>

# Prevent directory browsing
Options -Indexes

# Set security headers for PHP responses
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Optional: Restrict access to specific IP ranges (uncomment and modify as needed)
# <RequireAll>
#     Require ip 192.168.1
#     Require ip 10.0.0
# </RequireAll>
